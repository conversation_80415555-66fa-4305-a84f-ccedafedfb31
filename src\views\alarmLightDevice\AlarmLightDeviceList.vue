<template>
  <div class="p-2">

    <a-row :gutter="24">
       <!-- 左侧分组列表（固定宽度240px） -->
       <!-- 左侧路线列表 - 响应式优化 -->
      <a-col :xs="0" :sm="0" :md="6" :lg="5" :xl="4" :xxl="3">
        <div class="route-list-container">
          <h3 style="margin-bottom: 16px;">路线列表</h3>
          <a-menu
            mode="inline"
            :selectedKeys="[queryParam.lineId || 'all']"
            @select="handleLineSelect"
          >
            <!-- 全部选项 -->
            <a-menu-item key="all">全部</a-menu-item>
            <!-- 动态路线列表 -->
            <a-menu-item
              v-for="route in routeList"
              :key="route.id"
            >
              {{ route.name }}
            </a-menu-item>
          </a-menu>
        </div>
      </a-col>

      <!-- 主内容区域 - 响应式优化 -->
      <a-col :xs="24" :sm="24" :md="18" :lg="19" :xl="20" :xxl="21">
         <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol" style="background-color: #ffffff !important;">
        <a-row :gutter="[16, 8]">
          <a-col :xxl="6" :xl="7" :lg="8" :md="12" :sm="24" :xs="24">
            <a-form-item name="deviceName">
              <template #label><span title="设备名称">设备名称</span></template>
              <a-input placeholder="请输入设备名称" v-model:value="queryParam.deviceName" allow-clear ></a-input>
            </a-form-item>
          </a-col>
          <a-col :xxl="6" :xl="7" :lg="8" :md="12" :sm="24" :xs="24">
            <a-form-item name="status">
              <template #label><span title="设备状态">设备状态</span></template>
              <j-select-multiple placeholder="请选择设备状态" v-model:value="queryParam.status" dictCode="alarm_light_device_status" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xxl="12" :xl="10" :lg="8" :md="24" :sm="24" :xs="24">
            <div class="search-buttons">
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset">重置</a-button>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <div class="contner tech-container">
      <a-button type="primary" class="tech-button" v-auth="'alarmLightDevice:patrol_alarm_light_device:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
      <!-- 图片卡片展示区域 -->
        <!-- 移动端路线选择器 -->
        <div class="mobile-route-selector" v-if="isMobile">
          <a-select
            v-model:value="queryParam.lineId"
            placeholder="选择路线"
            style="width: 100%; margin-bottom: 16px;"
            @change="handleMobileRouteChange"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option
              v-for="route in routeList"
              :key="route.id"
              :value="route.id"
            >
              {{ route.name }}
            </a-select-option>
          </a-select>
        </div>

        <!-- 卡片网格布局 - 响应式优化 -->
        <a-row :span="24" class="model-row" :gutter="[16, 16]">
          <template v-if="deviceList && deviceList.length>0">
            <!--
              响应式布局说明：
              - xxl (≥1600px): 4列 (6/24)
              - xl (≥1200px): 4列 (6/24)
              - lg (≥992px): 3列 (8/24)
              - md (≥768px): 2列 (12/24)
              - sm (≥576px): 1列 (24/24)
              - xs (<576px): 1列 (24/24)
            -->
            <a-col :xxl="6" :xl="6" :lg="8" :md="12" :sm="24" :xs="24" v-for="device in deviceList" :key="device.id">
            <!-- 卡片内容 -->
            <a-card class="device-card tech-card tech-hover" :class="{ 'device-online': device.status == '1', 'device-offline': device.status != '1' }">
              <!-- 状态指示器 -->
              <div class="status-indicator" :class="{ 'online': device.status == '1', 'offline': device.status != '1' }"></div>

              <div class="device-header">
                <div class="device-image-container">
                  <img :src="getImage(device.image)" alt="设备图片" class="device-image">
                  <div class="image-overlay"></div>
                </div>
                <div class="device-info">
                  <h3 class="device-name tech-text">{{ device.deviceName }}</h3>
                  <div class="device-status-location">
                    <a-tag class="tech-status-tag" :class="device.status == '1' ? 'status-online' : 'status-offline'">
                      <span class="status-dot" :class="device.status == '1' ? 'dot-online' : 'dot-offline'"></span>
                      {{ device.status == '1' ? '在线' : '离线' }}
                    </a-tag>
                    <a-tag class="tech-location-tag" :class="getLocationClass(device.location)">
                      <Icon icon="ant-design:environment-outlined" class="location-icon" />
                      {{ getLocationText(device.location) }}
                    </a-tag>
                  </div>
                  <div class="device-sn">
                    <a-tag class="tech-sn-tag">
                      <Icon icon="ant-design:barcode-outlined" class="sn-icon" />
                      {{ device.snCode }}
                    </a-tag>
                  </div>
                </div>
              </div>

              <div class="device-details tech-details">
                <div class="detail-row">
                  <div class="detail-label">
                    <Icon icon="ant-design:setting-outlined" />
                    <span>型号</span>
                  </div>
                  <div class="detail-value">{{ device.model || '未知' }}</div>
                </div>
                <div class="detail-row">
                  <div class="detail-label">
                    <Icon icon="ant-design:clock-circle-outlined" />
                    <span>创建时间</span>
                  </div>
                  <div class="detail-value">{{ formatTime(device.createTime) }}</div>
                </div>
                <div class="detail-row volume-row">
                  <div class="detail-label">
                    <Icon icon="ant-design:sound-outlined" />
                    <span>音量</span>
                  </div>
                  <div class="volume-control">
                    <a-slider
                      v-model:value="device.volume"
                      :min="0"
                      :max="10"
                      class="tech-slider"
                      @change="(value) => handleVolumeChange(value, device)"
                    />
                    <span class="volume-value">{{ device.volume || 0 }}</span>
                  </div>
                </div>
              </div>

              <div class="device-actions tech-actions">
                <a-button type="text" class="action-btn edit-btn" @click="handleEdit(device)">
                  <Icon icon="ant-design:edit-outlined" />
                  <span>编辑</span>
                </a-button>
                <div class="action-divider"></div>
                <a-button type="text" class="action-btn delete-btn" @click="handleDelete(device)">
                  <Icon icon="ant-design:delete-outlined" />
                  <span>删除</span>
                </a-button>
                <div class="action-divider"></div>
                <a-dropdown placement="topCenter" :trigger="['click']">
                  <a-button type="text" class="action-btn more-btn">
                    <Icon icon="ant-design:more-outlined" />
                    <span>更多</span>
                  </a-button>
                  <template #overlay>
                    <a-menu class="tech-dropdown-menu">
                      <a-menu-item @click="handleDetail(device)">
                        <Icon icon="ant-design:eye-outlined" />
                        查看详情
                      </a-menu-item>
                      <a-menu-item @click="handleTest(device.id)">
                        <Icon icon="ant-design:experiment-outlined" />
                        设备测试
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </a-card>
          </a-col>
          </template>
          <template v-else>
            <div class="empty-container tech-empty">
              <a-empty description="暂无设备数据">
                <template #image>
                  <Icon icon="ant-design:robot-outlined" class="empty-icon" />
                </template>
              </a-empty>
            </div>
          </template>

        </a-row>

    <Pagination
      v-if="deviceList.length > 0"
      :current="pageNo"
      :page-size="pageSize"
      :page-size-options="pageSizeOptions"
      :total="total"
      :showQuickJumper="!isMobile"
      :showSizeChanger="!isMobile"
      :showTotal="(total, range) => isMobile ? `${range[0]}-${range[1]} / ${total}` : `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`"
      @change="handlePageChange"
      class="list-footer"
      :size="isMobile ? 'small' : 'default'"
    />
    </div>
     
    
    
    <!-- 表单区域 -->
    <AlarmLightDeviceModal ref="registerModal" @success="handleSuccess"></AlarmLightDeviceModal>
    <AlarmLightDeviceDetailModal ref="detailModal"></AlarmLightDeviceDetailModal>
      </a-col>
    </a-row> 

   
  </div>
</template>

<script lang="ts" name="alarmLightDevice-alarmLightDevice" setup>
  import { ref, reactive, onMounted, computed, onUnmounted } from 'vue';

  import { list, deleteOne, testDevcice, setVolume } from './AlarmLightDevice.api';
  import AlarmLightDeviceModal from './components/AlarmLightDeviceModal.vue'
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import { Pagination } from 'ant-design-vue';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItems } from '/@/api/common/api';
  import Icon from '/@/components/Icon/src/Icon.vue';
  import AlarmLightDeviceDetailModal from './components/AlarmLightDeviceDetailModal.vue';



  const formRef = ref();
  const queryParam = reactive<any>({
    lineId: '',  // 从数组改为字符串
    status: ''
  });
  const deviceList = ref([]);
  const registerModal = ref();
  const detailModal = ref();
  const { createMessage } = useMessage();
  const routeList = ref<any[]>([]);

  // 响应式检测
  const windowWidth = ref(window.innerWidth);
  const isMobile = computed(() => windowWidth.value < 768);

  // 监听窗口大小变化
  const handleResize = () => {
    windowWidth.value = window.innerWidth;
  };

  //当前页数
  const pageNo = ref<number>(1);
  //每页条数
  const pageSize = ref<number>(8);
  //总条数
  const total = ref<number>(0);
  //可选择的页数
  const pageSizeOptions = ref<any>(['8', '16', '24', '32']);
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  //页面初始化执行列表查询
  onMounted(async () => {
    // 添加窗口大小监听
    window.addEventListener('resize', handleResize);

    try{
        const dictData = await getDictItems('patrol_line,name,id,del_flag = 0');  // 假设路线字典code为'patrol_line'

        routeList.value = dictData.map(item => ({
          id: item.value,
          name: item.text
        }));

        await reload();
    }catch(error){
      console.error('初始化失败:', error);
      createMessage.error("初始化失败，请稍后重试");
    }
  })

  // 组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  })

   /**
   * 路线选择事件处理
   * @param param0
   */
   const handleLineSelect = ({ key }: { key: string }) => {
    queryParam.lineId = key === 'all' ? null : key;  // 'all' 对应全部
    reload();  // 触发查询
  };

  /**
   * 移动端路线选择处理
   * @param value
   */
  const handleMobileRouteChange = (value: string) => {
    queryParam.lineId = value || null;
    reload();
  };

  
   /**
    * 重新加载数据
   */
  function reload() {
   
    let params = {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      column: 'createTime',
      order: 'desc'
    };
    Object.assign(params, queryParam);
    list(params).then((res) => {
     console.log(res)
      if (res.success) {
       
        deviceList.value = res.result.records;
        console.log(deviceList.value);
        total.value = res.result.total;
      } else {
        deviceList.value = [];
        total.value = 0;
      }
    });
  }

  /**
       * 分页改变事件
       * @param page
       * @param current
       */
       function handlePageChange(page, current) {
        pageNo.value = page;
        pageSize.value = current;
        reload();
      }

  /**
   * 调整音量
   * @param value
   * @param device
   */
   const handleVolumeChange = async (value: number, device: any) => {
    // 防止重复调用
    if (device._volumeUpdating) {
      return;
    }

    device._volumeUpdating = true;
    const originalVolume = device.volume;

    try {
      // 更新设备音量值
      device.volume = value;

      await setVolume({id: device.id, volume: value})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
        } else {
          createMessage.warning(res.message);
         
        }
      })
      console.log('音量已更新:', value);
      reload();
    } catch (error) {
      console.error('更新音量失败:', error);
      createMessage.warning("音量更新失败");
      // 回滚到原始值
      device.volume = originalVolume;
    } finally {
      // 延迟重置标志，防止快速连续操作
      setTimeout(() => {
        device._volumeUpdating = false;
      }, 300);
    }
};

function handleTest(id){
  testDevcice({id:id})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
        } else {
          createMessage.warning(res.message);
        }
    })
}



  /**
   * 查看图片
  * @param imageUrl
  */
  const getImage = (imageUrl) => {
    return getFileAccessHttpUrl(imageUrl);
  }

  /**
   * 获取位置文本
   */
  const getLocationText = (location) => {
    const locationMap = {
      '1': '活动室',
      '2': '分控中心',
      '3': '指挥中心'
    };
    return locationMap[location] || '未知位置';
  }

  /**
   * 获取位置样式类
   */
  const getLocationClass = (location) => {
    const classMap = {
      '1': 'location-activity',
      '2': 'location-control',
      '3': 'location-command'
    };
    return classMap[location] || 'location-unknown';
  }

  /**
   * 格式化时间
   */
  const formatTime = (timeStr) => {
    if (!timeStr) return '未知';
    try {
      const date = new Date(timeStr);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return timeStr;
    }
  }



  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    detailModal.value.showModal(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
 
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }
   


  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
 
    //刷新数据
    reload();
  }
  




</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 10px;
    background-color: #ffffff;

    .search-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 16px;
    }

    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }

  /* 查询按钮响应式样式 */
  @media (max-width: 767px) {
    .jeecg-basic-table-form-container {
      padding: 8px;

      .search-buttons {
        justify-content: center;
        width: 100%;
        margin-top: 8px;
      }

      .ant-form-item {
        margin-bottom: 12px;
      }
    }
  }

  @media (max-width: 480px) {
    .jeecg-basic-table-form-container {
      .search-buttons {
        flex-direction: column;
        gap: 8px;

        .ant-btn {
          width: 100%;
        }
      }
    }
  }
  /* 科技感容器 */
  .tech-container {
    margin-top: 10px;
    padding: 24px;
    background:
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
      linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
  }

  .tech-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.02) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
  }

  /* 科技感按钮 */
  .tech-button {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 8px;
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.2),
      0 2px 8px rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
  }

  .tech-button:hover {
    transform: translateY(-2px);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.3),
      0 4px 12px rgba(59, 130, 246, 0.15);
  }

  .model-row {
    margin-top: 20px;
    position: relative;
    z-index: 1;
  }

  /* 科技感卡片 */
  .device-card.tech-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
      0 4px 20px rgba(59, 130, 246, 0.08),
      0 2px 10px rgba(6, 182, 212, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .device-card.tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .device-card.tech-hover:hover {
    transform: translateY(-4px);
    border-color: rgba(59, 130, 246, 0.2);
    box-shadow:
      0 8px 30px rgba(59, 130, 246, 0.12),
      0 4px 20px rgba(6, 182, 212, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .device-card.tech-hover:hover::before {
    opacity: 1;
  }

  /* 在线/离线状态样式 */
  .device-online {
    border-left: 4px solid #10b981;
  }

  .device-offline {
    border-left: 4px solid #ef4444;
  }

  /* 状态指示器 */
  .status-indicator {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    z-index: 2;
  }

  .status-indicator.online {
    background: #10b981;
    box-shadow:
      0 0 0 3px rgba(16, 185, 129, 0.2),
      0 0 8px rgba(16, 185, 129, 0.4);
    animation: pulse-online 2s infinite;
  }

  .status-indicator.offline {
    background: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  }

  @keyframes pulse-online {
    0%, 100% {
      box-shadow:
        0 0 0 3px rgba(16, 185, 129, 0.2),
        0 0 8px rgba(16, 185, 129, 0.4);
    }
    50% {
      box-shadow:
        0 0 0 6px rgba(16, 185, 129, 0.1),
        0 0 12px rgba(16, 185, 129, 0.6);
    }
  }

  /* 设备头部 */
  .device-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 16px;
  }

  .device-image-container {
    position: relative;
    flex-shrink: 0;
  }

  .device-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    border: 2px solid rgba(59, 130, 246, 0.1);
    object-fit: cover;
    transition: all 0.3s ease;
  }

  .device-image:hover {
    border-color: rgba(59, 130, 246, 0.3);
    transform: scale(1.05);
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .device-image:hover + .image-overlay {
    opacity: 1;
  }

  .device-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .device-name.tech-text {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    background: linear-gradient(135deg, #1e293b, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 状态和位置标签 - 一排显示 */
  .device-status-location {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: nowrap;
  }

  .tech-status-tag {
    border: none;
    border-radius: 8px;
    padding: 4px 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
  }

  .tech-status-tag.status-online {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  .tech-status-tag.status-offline {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .status-dot.dot-online {
    background: #10b981;
    animation: pulse-dot 2s infinite;
  }

  .status-dot.dot-offline {
    background: #ef4444;
  }

  @keyframes pulse-dot {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .tech-location-tag {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 4px 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .tech-location-tag.location-activity {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.2);
  }

  .tech-location-tag.location-control {
    background: rgba(6, 182, 212, 0.1);
    color: #06b6d4;
    border-color: rgba(6, 182, 212, 0.2);
  }

  .tech-location-tag.location-command {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border-color: rgba(139, 92, 246, 0.2);
  }

  .device-sn {
    margin-top: 4px;
  }

  .tech-sn-tag {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
    border-radius: 6px;
    padding: 2px 8px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  /* 设备详情 */
  .tech-details {
    margin: 16px 0;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.05);
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .detail-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    font-weight: 500;
    font-size: 14px;
  }

  .detail-value {
    color: #1e293b;
    font-weight: 500;
    font-size: 14px;
  }

  .volume-row {
    align-items: center;
    min-height: 36px;
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    max-width: 200px;
    height: 36px;
  }

  .tech-slider {
    flex: 1;
    position: relative;
    padding: 8px 0;
  }

  .tech-slider :deep(.ant-slider) {
    margin: 0;
    padding: 0;
  }

  .tech-slider :deep(.ant-slider-rail) {
    background: rgba(59, 130, 246, 0.1);
    height: 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .tech-slider :deep(.ant-slider-track) {
    background: linear-gradient(90deg, #3b82f6, #06b6d4);
    height: 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .tech-slider:hover :deep(.ant-slider-rail) {
    background: rgba(59, 130, 246, 0.15);
  }

  .tech-slider:hover :deep(.ant-slider-track) {
    background: linear-gradient(90deg, #1d4ed8, #0891b2);
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
  }

  .tech-slider :deep(.ant-slider-handle) {
    width: 20px;
    height: 20px;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    background: #ffffff;
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.3),
      0 0 0 0 rgba(59, 130, 246, 0.2);
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: -7px;
    margin-left: -10px;
    position: relative;
  }

  .tech-slider :deep(.ant-slider-handle:hover) {
    border-color: #1d4ed8;
    background: #f8fafc;
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.4),
      0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: scale(1.1);
    outline: none;
  }

  .tech-slider :deep(.ant-slider-handle:focus) {
    border-color: #1d4ed8;
    background: #f8fafc;
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.4),
      0 0 0 4px rgba(59, 130, 246, 0.2);
    outline: none;
  }

  .tech-slider :deep(.ant-slider-handle:active) {
    border-color: #1e40af;
    background: #f1f5f9;
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.5),
      0 0 0 6px rgba(59, 130, 246, 0.15);
    transform: scale(1.05);
  }

  .tech-slider :deep(.ant-slider-handle::before) {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: #3b82f6;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }

  .tech-slider :deep(.ant-slider-handle:hover::before) {
    background: #1d4ed8;
    width: 8px;
    height: 8px;
  }

  .tech-slider :deep(.ant-slider-handle:active::before) {
    background: #1e40af;
    width: 4px;
    height: 4px;
  }

  .tech-slider :deep(.ant-slider-handle::after) {
    display: none;
  }

  .volume-value {
    min-width: 20px;
    text-align: center;
    font-weight: 600;
    color: #3b82f6;
    font-size: 14px;
  }

  /* 操作按钮区域 */
  .tech-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
  }

  .action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    background: transparent;
  }

  .action-btn:hover {
    transform: translateY(-1px);
  }

  .edit-btn {
    color: #3b82f6;
  }

  .edit-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
  }

  .delete-btn {
    color: #ef4444;
  }

  .delete-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
  }

  .more-btn {
    color: #6b7280;
  }

  .more-btn:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
  }

  .action-divider {
    width: 1px;
    height: 20px;
    background: rgba(59, 130, 246, 0.1);
    margin: 0 4px;
  }

  /* 下拉菜单 */
  .tech-dropdown-menu {
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
    overflow: hidden;
  }

  .tech-dropdown-menu :deep(.ant-menu-item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
  }

  .tech-dropdown-menu :deep(.ant-menu-item:hover) {
    background: rgba(59, 130, 246, 0.05);
  }
.list-footer {
    text-align: right;
    margin-top: 5px;
  }

  // 新增：左侧路线列表样式
 .route-list-container {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    height: calc(100vh - 100px);
    overflow-y: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: #fff;
  }

  .route-list-container h3 {
    color: #262626;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .route-list-container :deep(.ant-menu) {
    border-right: none;
  }

  .route-list-container :deep(.ant-menu-item) {
    padding: 8px 12px;
    border-radius: 4px;
    margin: 4px 0;
    transition: all 0.2s;
  }

  .route-list-container :deep(.ant-menu-item:hover) {
    background-color: #f5f7fa;
  }

  .route-list-container :deep(.ant-menu-item-selected) {
    background-color: #e6f4ff;
    color: #1677ff;
    font-weight: 500;
  }

  /* 自定义滚动条样式 */
  .route-list-container::-webkit-scrollbar {
    width: 6px;
  }
  
  .route-list-container::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
  }
  
  .route-list-container::-webkit-scrollbar-track {
    background: #f5f5f5;
  }
  /* 空状态 */
  .tech-empty {
    width: 100%;
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 16px;
    border: 2px dashed rgba(59, 130, 246, 0.2);
  }

  .empty-icon {
    font-size: 48px;
    color: rgba(59, 130, 246, 0.4);
    margin-bottom: 16px;
  }

  /* 移动端路线选择器样式 */
  .mobile-route-selector {
    display: none;
  }

  /* 响应式设计 */
  /* 大屏幕优化 (≥1600px) */
  @media (min-width: 1600px) {
    .device-card.tech-card {
      padding: 24px;
    }

    .device-name.tech-text {
      font-size: 20px;
    }

    .device-image {
      width: 90px;
      height: 90px;
    }
  }

  /* 中等屏幕优化 (992px - 1199px) */
  @media (max-width: 1199px) and (min-width: 992px) {
    .device-card.tech-card {
      padding: 18px;
    }

    .device-name.tech-text {
      font-size: 17px;
    }

    .device-image {
      width: 75px;
      height: 75px;
    }
  }

  /* 小屏幕优化 (768px - 991px) */
  @media (max-width: 991px) and (min-width: 768px) {
    .device-card.tech-card {
      padding: 16px;
    }

    .device-name.tech-text {
      font-size: 16px;
    }

    .device-image {
      width: 70px;
      height: 70px;
    }

    .volume-control {
      max-width: 150px;
    }
  }

  /* 平板和移动端 (≤767px) */
  @media (max-width: 767px) {
    /* 显示移动端路线选择器 */
    .mobile-route-selector {
      display: block;
    }

    /* 隐藏左侧路线列表 */
    .route-list-container {
      display: none;
    }

    .tech-container {
      padding: 12px;
      margin-top: 0;
    }

    .device-card.tech-card {
      padding: 16px;
      margin-bottom: 16px;
    }

    .device-header {
      flex-direction: row;
      align-items: flex-start;
      text-align: left;
      gap: 12px;
    }

    .device-image {
      width: 60px;
      height: 60px;
    }

    .device-name.tech-text {
      font-size: 16px;
      line-height: 1.3;
      word-break: break-word;
    }

    .device-status-location {
      flex-wrap: wrap;
      gap: 6px;
    }

    .tech-status-tag,
    .tech-location-tag {
      font-size: 12px;
      padding: 2px 8px;
      white-space: nowrap;
    }

    .tech-sn-tag {
      font-size: 11px;
      padding: 1px 6px;
      word-break: break-all;
      max-width: 100%;
    }

    .detail-row {
      margin-bottom: 8px;
      padding: 6px 0;
      flex-wrap: wrap;
      gap: 4px;
    }

    .detail-label {
      font-size: 13px;
      min-width: 60px;
      flex-shrink: 0;
    }

    .detail-value {
      font-size: 13px;
      word-break: break-word;
      text-align: right;
      flex: 1;
    }

    .volume-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .volume-control {
      width: 100%;
      max-width: none;
      min-width: 150px;
    }

    .tech-slider {
      width: 100%;
    }

    .tech-actions {
      flex-direction: row;
      gap: 4px;
      flex-wrap: wrap;
    }

    .action-btn {
      flex: 1;
      font-size: 12px;
      padding: 6px 8px;
      min-width: 0;
    }

    .action-btn span {
      display: none;
    }

    .action-divider {
      height: 16px;
      margin: 0 2px;
    }
  }

  /* 超小屏幕 (≤480px) */
  @media (max-width: 480px) {
    .tech-container {
      padding: 8px;
    }

    .device-card.tech-card {
      padding: 12px;
      margin-bottom: 12px;
    }

    .device-header {
      gap: 8px;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .device-image-container {
      align-self: center;
    }

    .device-image {
      width: 50px;
      height: 50px;
    }

    .device-info {
      width: 100%;
      align-items: center;
    }

    .device-name.tech-text {
      font-size: 14px;
      text-align: center;
      margin-bottom: 8px;
    }

    .device-status-location {
      justify-content: center;
      width: 100%;
    }

    .tech-status-tag,
    .tech-location-tag {
      font-size: 11px;
      padding: 2px 6px;
    }

    .tech-sn-tag {
      font-size: 10px;
      padding: 1px 4px;
    }

    .detail-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
      margin-bottom: 6px;
      padding: 4px 0;
    }

    .detail-label {
      font-size: 12px;
      width: 100%;
    }

    .detail-value {
      font-size: 12px;
      width: 100%;
      text-align: left;
      font-weight: 400;
    }

    .volume-row {
      align-items: flex-start;
    }

    .volume-control {
      width: 100%;
      max-width: none;
      margin-top: 4px;
    }

    .tech-actions {
      gap: 2px;
      margin-top: 12px;
      padding-top: 12px;
    }

    .action-btn {
      padding: 4px 6px;
      font-size: 11px;
    }

    .action-divider {
      height: 14px;
      margin: 0 1px;
    }
  }

  /* 全局图标样式 */
  .location-icon,
  .sn-icon {
    font-size: 12px;
  }

  /* 列表底部分页 */
  .list-footer {
    text-align: right;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
  }

  /* 分页响应式样式 */
  @media (max-width: 767px) {
    .list-footer {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
    }

    .list-footer :deep(.ant-pagination) {
      justify-content: center;
    }

    .list-footer :deep(.ant-pagination-item) {
      margin: 0 2px;
      min-width: 28px;
      height: 28px;
      line-height: 26px;
    }

    .list-footer :deep(.ant-pagination-prev),
    .list-footer :deep(.ant-pagination-next) {
      min-width: 28px;
      height: 28px;
      line-height: 26px;
    }
  }

  @media (max-width: 480px) {
    .list-footer {
      margin-top: 12px;
      padding-top: 12px;
    }

    .list-footer :deep(.ant-pagination-item) {
      margin: 0 1px;
      min-width: 24px;
      height: 24px;
      line-height: 22px;
      font-size: 12px;
    }

    .list-footer :deep(.ant-pagination-prev),
    .list-footer :deep(.ant-pagination-next) {
      min-width: 24px;
      height: 24px;
      line-height: 22px;
    }

    .list-footer :deep(.ant-pagination-total-text) {
      font-size: 12px;
    }
  }
</style>
